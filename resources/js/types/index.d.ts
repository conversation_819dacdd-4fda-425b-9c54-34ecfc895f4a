export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    auth: {
        user: User;
    };
    filters: Filters; // Make filters always present
};

interface Filters {
    search: string | null;
    status: 'active' | 'inactive' | null;
    tag: string | null;
    min_executions: number | null;
    max_executions: number | null;
    last_executed_before: string | null;
    last_executed_after: string | null;
}

export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string;
}

export interface Template {
    id: number;
    name: string;
    description: string;
    tags: string;
    schema: string;
    created_at: string;
    updated_at: string;
}