const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"dashboard":{"uri":"\/","methods":["GET","HEAD"]},"instances.index":{"uri":"instances","methods":["GET","HEAD"]},"instances.create":{"uri":"instances\/create","methods":["GET","HEAD"]},"instances.store":{"uri":"instances","methods":["POST"]},"instances.show":{"uri":"instances\/{instance}","methods":["GET","HEAD"],"parameters":["instance"]},"instances.edit":{"uri":"instances\/{instance}\/edit","methods":["GET","HEAD"],"parameters":["instance"]},"instances.update":{"uri":"instances\/{instance}","methods":["PUT","PATCH"],"parameters":["instance"]},"instances.destroy":{"uri":"instances\/{instance}","methods":["DELETE"],"parameters":["instance"]},"workflows.index":{"uri":"workflows","methods":["GET","HEAD"]},"workflows.create":{"uri":"workflows\/create","methods":["GET","HEAD"]},"workflows.store":{"uri":"workflows","methods":["POST"]},"workflows.show":{"uri":"workflows\/{workflow}","methods":["GET","HEAD"],"parameters":["workflow"]},"workflows.edit":{"uri":"workflows\/{workflow}\/edit","methods":["GET","HEAD"],"parameters":["workflow"]},"workflows.update":{"uri":"workflows\/{workflow}","methods":["PUT","PATCH"],"parameters":["workflow"]},"workflows.destroy":{"uri":"workflows\/{workflow}","methods":["DELETE"],"parameters":["workflow"]},"workflows.executions.index":{"uri":"workflows\/{workflow}\/executions","methods":["GET","HEAD"],"parameters":["workflow"],"bindings":{"workflow":"id"}},"templates.index":{"uri":"templates","methods":["GET","HEAD"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
