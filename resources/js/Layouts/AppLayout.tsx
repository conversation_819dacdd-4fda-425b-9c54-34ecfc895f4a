import React, { useState } from 'react';
import { <PERSON> } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';

export default function AppLayout({ children }: { children: React.ReactNode }) {
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    return (
        <div className="flex h-screen bg-background text-foreground">
            {/* Sidebar */}
            <aside
                className={`flex-shrink-0 border-r border-border transition-all duration-300 ease-in-out
                ${isSidebarOpen ? 'w-64' : 'w-0 overflow-hidden md:w-64 md:overflow-visible'}`}
            >
                <div className="p-4">
                    <h1 className="text-2xl font-bold">
                        <Link href={route('dashboard')}>N8N Dashboard</Link>
                    </h1>
                </div>
                <nav className="px-4">
                    <ul>
                        <li>
                            <Link href={route('dashboard')} className="block py-2 text-muted-foreground hover:text-foreground">
                                Dashboard
                            </Link>
                        </li>
                        <li>
                            <Link href={route('instances.index')} className="block py-2 text-muted-foreground hover:text-foreground">
                                Instances
                            </Link>
                        </li>
                        <li>
                            <Link href={route('workflows.index')} className="block py-2 text-muted-foreground hover:text-foreground">
                                Workflows
                            </Link>
                        </li>
                        <li>
                            <Link href={route('templates.index')} className="block py-2 text-muted-foreground hover:text-foreground">
                                Templates
                            </Link>
                        </li>
                    </ul>
                </nav>
            </aside>

            {/* Main Content Area */}
            <main className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="flex items-center justify-between p-4 border-b border-border">
                    <Button variant="ghost" size="icon" onClick={toggleSidebar} className="md:hidden">
                        <Menu className="h-6 w-6" />
                    </Button>
                    <h2 className="text-xl font-semibold">Page Title</h2> {/* Placeholder for page title */}
                    {/* User/Theme Toggle will go here */}
                </header>

                {/* Page Content */}
                <div className="flex-1 overflow-y-auto">
                    {children}
                </div>
            </main>
        </div>
    );
}
