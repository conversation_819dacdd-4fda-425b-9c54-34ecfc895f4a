import React, { useEffect, useState } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';

interface N8nInstance {
    id: number;
    name: string;
    url: string;
    health_status: string;
    last_checked_at: string;
}

export default function Index({ instances: initialInstances }: PageProps<{ instances: N8nInstance[] }>) {
    const [instances, setInstances] = useState(initialInstances);

    const checkHealth = async (instanceId: number) => {
        const response = await fetch(route('instances.check-health', instanceId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
        });
        const updatedInstance = await response.json();
        setInstances(instances.map(instance => instance.id === updatedInstance.id ? updatedInstance : instance));
    };

    useEffect(() => {
        instances.forEach(instance => checkHealth(instance.id));
    }, []);

    return (
        <AppLayout>
            <div className="p-8">
                <div className="flex justify-between items-center">
                    <h2 className="text-3xl font-bold">Manage Instances</h2>
                    <Link href={route('instances.create')}>
                        <Button>Add Instance</Button>
                    </Link>
                </div>

                <div className="mt-8">
                    {instances.length === 0 ? (
                        <p className="text-muted-foreground">
                            You haven't added any N8N instances yet.
                        </p>
                    ) : (
                        <ul className="space-y-4">
                            {instances.map((instance) => (
                                <li key={instance.id} className="p-4 border rounded-lg flex items-center justify-between">
                                    <div>
                                        <p className="font-semibold">{instance.name}</p>
                                        <p className="text-sm text-muted-foreground">{instance.url}</p>
                                        <p className="text-xs text-muted-foreground">Status: {instance.health_status}</p>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button variant="outline" size="sm" onClick={() => checkHealth(instance.id)}>Check Health</Button>
                                        <Link href={route('instances.analytics', instance.id)}>
                                            <Button variant="outline" size="sm">View Analytics</Button>
                                        </Link>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
