import React from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { usePage } from '@inertiajs/react';
import { PageProps, Template } from '@/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function Index() {
    const { templates } = usePage<PageProps & { templates: Template[] }>().props;

    return (
        <AppLayout>
            <div className="p-8">
                <h2 className="text-3xl font-bold">Template Marketplace</h2>
                <p className="mt-2 text-muted-foreground">
                    Browse and use workflow templates.
                </p>

                <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {templates.map((template) => (
                        <Card key={template.id}>
                            <CardHeader>
                                <CardTitle>{template.name}</CardTitle>
                                <CardDescription>{template.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {JSON.parse(template.tags).map((tag: string) => (
                                    <Badge key={tag} variant="secondary" className="mr-2 mb-2">
                                        {tag}
                                    </Badge>
                                ))}
                            </CardContent>
                            <CardFooter>
                                <p className="text-sm text-muted-foreground">
                                    Created at {new Date(template.created_at).toLocaleDateString()}
                                </p>
                            </CardFooter>
                        </Card>
                    ))}
                </div>
            </div>
        </AppLayout>
    );
}