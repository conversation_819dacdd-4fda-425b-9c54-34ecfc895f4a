import React, { useEffect, useState } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import MetricCard from '@/components/MetricCard';
import LineChartCard from '@/components/LineChartCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import axios from 'axios';
import { Activity, ArrowUpCircle, CheckCircle2, Clock, XCircle } from 'lucide-react';

interface DashboardData {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgExecutionTime: number;
    monthlyExecutions: { name: string; executions: number }[];
    recentExecutions: any[]; // Replace any with a proper type
    topWorkflows: any[]; // Replace any with a proper type
}

export default function Index() {
    const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchDashboardData = () => {
            axios.get('/api/dashboard-data')
                .then(response => {
                    setDashboardData(response.data);
                })
                .catch(err => {
                    setError('Failed to load dashboard data.');
                    console.error(err);
                })
                .finally(() => {
                    setLoading(false);
                });
        };

        fetchDashboardData(); // Initial fetch

        // Listen for real-time updates
        window.Echo.channel('workflow-executions')
            .listen('.workflow.execution.updated', (e: any) => {
                console.log('Real-time update received:', e);
                // Re-fetch data to update dashboard
                fetchDashboardData();
            });

        // Clean up the listener when the component unmounts
        return () => {
            window.Echo.leaveChannel('workflow-executions');
        };
    }, []);

    if (loading) {
        return (
            <AppLayout>
                <div className="p-8">
                    <h2 className="text-3xl font-bold">Analytics Dashboard</h2>
                    <p className="mt-2 text-muted-foreground">Loading dashboard data...</p>
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout>
                <div className="p-8">
                    <h2 className="text-3xl font-bold">Analytics Dashboard</h2>
                    <p className="mt-2 text-destructive">{error}</p>
                </div>
            </AppLayout>
        );
    }

    if (!dashboardData) {
        return (
            <AppLayout>
                <div className="p-8">
                    <h2 className="text-3xl font-bold">Analytics Dashboard</h2>
                    <p className="mt-2 text-muted-foreground">No data available.</p>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <div className="p-8">
                <header className="flex items-center justify-between space-y-2 mb-8">
                    <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
                </header>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <MetricCard
                        title="Total Executions"
                        value={dashboardData.totalExecutions.toLocaleString()}
                        description="Total workflows executed."
                        icon={<Activity className="h-4 w-4 text-muted-foreground" />}
                    />
                    <MetricCard
                        title="Successful Executions"
                        value={dashboardData.successfulExecutions.toLocaleString()}
                        description="Successfully completed workflows."
                        icon={<CheckCircle2 className="h-4 w-4 text-muted-foreground" />}
                    />
                    <MetricCard
                        title="Failed Executions"
                        value={dashboardData.failedExecutions.toLocaleString()}
                        description="Workflows with errors."
                        icon={<XCircle className="h-4 w-4 text-muted-foreground" />}
                    />
                    <MetricCard
                        title="Avg. Execution Time"
                        value={`${dashboardData.avgExecutionTime}s`}
                        description="Average workflow completion time."
                        icon={<Clock className="h-4 w-4 text-muted-foreground" />}
                    />
                </div>

                <div className="grid gap-4 mt-8 md:grid-cols-2 lg:grid-cols-7">
                    <LineChartCard
                        title="Monthly Executions"
                        data={dashboardData.monthlyExecutions}
                        dataKey="name"
                        lineKey="executions"
                    />

                    <Card className="col-span-3">
                        <CardHeader>
                            <CardTitle>Recent Activity</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Workflow</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Time</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {dashboardData.recentExecutions.map((execution) => (
                                        <TableRow key={execution.id}>
                                            <TableCell>{execution.workflow.name}</TableCell>
                                            <TableCell>
                                                <Badge variant={execution.status === 'completed' ? 'default' : 'destructive'}>
                                                    {execution.status}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{new Date(execution.created_at).toLocaleTimeString()}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </div>

                 <div className="grid gap-4 mt-8">
                    <Card>
                        <CardHeader>
                            <CardTitle>Top Workflows</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Workflow</TableHead>
                                        <TableHead>Executions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {dashboardData.topWorkflows.map((workflow) => (
                                        <TableRow key={workflow.id}>
                                            <TableCell>{workflow.name}</TableCell>
                                            <TableCell>{workflow.executions_count}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
