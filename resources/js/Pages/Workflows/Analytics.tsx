import React, { useEffect, useState } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import MetricCard from '@/components/MetricCard';
import LineChartCard from '@/components/LineChartCard';
import axios from 'axios';

interface Workflow {
    id: number;
    name: string;
}

interface AnalyticsData {
    workflow: Workflow;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgExecutionTime: number;
    monthlyExecutions: { name: string; executions: number }[];
}

export default function Analytics({ workflow }: PageProps<{ workflow: Workflow }>) {
    const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        axios.get(`/api/analytics/workflow/${workflow.id}`)
            .then(response => {
                setAnalyticsData(response.data);
            })
            .catch(err => {
                setError('Failed to load analytics data.');
                console.error(err);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [workflow.id]);

    if (loading) {
        return (
            <AppLayout>
                <div className="p-8">
                    <h2 className="text-3xl font-bold">Analytics for {workflow.name}</h2>
                    <p className="mt-2 text-muted-foreground">Loading analytics data...</p>
                </div>
            </AppLayout>
        );
    }

    if (error) {
        return (
            <AppLayout>
                <div className="p-8">
                    <h2 className="text-3xl font-bold">Analytics for {workflow.name}</h2>
                    <p className="mt-2 text-destructive">{error}</p>
                </div>
            </AppLayout>
        );
    }

    if (!analyticsData) {
        return (
            <AppLayout>
                <div className="p-8">
                    <h2 className="text-3xl font-bold">Analytics for {workflow.name}</h2>
                    <p className="mt-2 text-muted-foreground">No data available.</p>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <div className="p-8">
                <h2 className="text-3xl font-bold">Analytics for {analyticsData.workflow.name}</h2>
                <p className="mt-2 text-muted-foreground">
                    Detailed analytics for this workflow.
                </p>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-8">
                    <MetricCard
                        title="Total Executions"
                        value={analyticsData.totalExecutions.toLocaleString()}
                        description="Executions for this workflow."
                    />
                    <MetricCard
                        title="Successful Executions"
                        value={analyticsData.successfulExecutions.toLocaleString()}
                        description="Successful executions for this workflow."
                    />
                    <MetricCard
                        title="Failed Executions"
                        value={analyticsData.failedExecutions.toLocaleString()}
                        description="Failed executions for this workflow."
                    />
                    <MetricCard
                        title="Avg. Execution Time"
                        value={`${analyticsData.avgExecutionTime}s`}
                        description="Average time taken for this workflow to complete."
                    />
                </div>

                <div className="grid gap-4 mt-8">
                    <LineChartCard
                        title="Monthly Executions for this Workflow"
                        data={analyticsData.monthlyExecutions}
                        dataKey="month"
                        lineKey="executions"
                    />
                </div>
            </div>
        </AppLayout>
    );
}
