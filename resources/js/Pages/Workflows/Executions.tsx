import React from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { Button } from '@/components/ui/button';
import { Inertia } from '@inertiajs/inertia';

interface Workflow {
    id: number;
    name: string;
}

interface WorkflowExecution {
    id: number;
    n8n_execution_id: string;
    status: string;
    started_at: string;
    finished_at: string;
    execution_time: number;
    data: any;
}

export default function Executions({ workflow, executions }: PageProps<{ workflow: Workflow, executions: WorkflowExecution[] }>) {
    const handleRetry = (workflowId: number, executionId: string) => {
        if (confirm('Are you sure you want to retry this execution?')) {
            Inertia.post(`/workflows/${workflowId}/executions/${executionId}/retry`);
        }
    };

    const handleCancel = (workflowId: number, executionId: string) => {
        if (confirm('Are you sure you want to cancel this execution?')) {
            Inertia.post(`/workflows/${workflowId}/executions/${executionId}/cancel`);
        }
    };

    return (
        <AppLayout>
            <div className="p-8">
                <h2 className="text-3xl font-bold">Executions for {workflow.name}</h2>
                <p className="mt-2 text-muted-foreground">
                    A list of workflow executions will appear here.
                </p>

                <div className="mt-8">
                    {executions.length === 0 ? (
                        <p className="text-muted-foreground">
                            No executions found for this workflow.
                        </p>
                    ) : (
                        <ul className="space-y-4">
                            {executions.map((execution) => (
                                <li key={execution.id} className="p-4 border rounded-lg">
                                    <div className="flex justify-between items-center">
                                        <p className="font-semibold">Execution ID: {execution.n8n_execution_id}</p>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${execution.status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                            {execution.status}
                                        </span>
                                    </div>
                                    <p className="text-sm text-muted-foreground">Started: {new Date(execution.started_at).toLocaleString()}</p>
                                    {execution.finished_at && <p className="text-sm text-muted-foreground">Finished: {new Date(execution.finished_at).toLocaleString()}</p>}
                                    {execution.execution_time && <p className="text-sm text-muted-foreground">Duration: {execution.execution_time}ms</p>}

                                    {execution.status === 'failed' && execution.data?.error && (
                                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                                            <p className="text-sm font-medium text-red-800">Error Details:</p>
                                            <pre className="text-xs text-red-700 mt-1 overflow-auto max-h-32">{JSON.stringify(execution.data.error, null, 2)}</pre>
                                        </div>
                                    )}

                                    {execution.data && (
                                        <details className="mt-2 text-xs text-muted-foreground">
                                            <summary>Raw Data</summary>
                                            <pre className="mt-1 overflow-auto max-h-32">{JSON.stringify(execution.data, null, 2)}</pre>
                                        </details>
                                    )}
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
