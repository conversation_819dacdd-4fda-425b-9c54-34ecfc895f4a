import React, { useState, useEffect } from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import { Link } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import debounce from 'lodash.debounce';

interface Workflow {
    id: number;
    n8n_instance_id: number;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags: string[];
    nodes: any;
    connections: any;
    last_sync_at: string;
}

interface Filters {
    search: string | null;
    status: 'active' | 'inactive' | null;
    tag: string | null;
    min_executions: number | null;
    max_executions: number | null;
    last_executed_before: string | null;
    last_executed_after: string | null;
}

export default function Index({ workflows, filters }: PageProps<{ workflows: Workflow[], filters: Filters }>) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || '');
    const [tag, setTag] = useState(filters.tag || '');
    const [minExecutions, setMinExecutions] = useState(filters.min_executions || '');
    const [maxExecutions, setMaxExecutions] = useState(filters.max_executions || '');
    const [lastExecutedBefore, setLastExecutedBefore] = useState(filters.last_executed_before || '');
    const [lastExecutedAfter, setLastExecutedAfter] = useState(filters.last_executed_after || '');

    const handleToggle = (id: number, is_active: boolean) => {
        Inertia.put(route('workflows.update', id), { is_active: is_active });
    };

    useEffect(() => {
        const handler = debounce(() => {
            const query = {};
            if (search) query.search = search;
            if (status) query.status = status;
            if (tag) query.tag = tag;
            if (minExecutions) query.min_executions = minExecutions;
            if (maxExecutions) query.max_executions = maxExecutions;
            if (lastExecutedBefore) query.last_executed_before = lastExecutedBefore;
            if (lastExecutedAfter) query.last_executed_after = lastExecutedAfter;

            Inertia.get(route('workflows.index'), query, {
                preserveState: true,
                replace: true,
            });
        }, 300);

        handler();

        return () => {
            handler.cancel();
        };
    }, [search, status, tag, minExecutions, maxExecutions, lastExecutedBefore, lastExecutedAfter]);

    return (
        <AppLayout>
            <div className="p-8">
                <div className="flex justify-between items-center">
                    <h2 className="text-3xl font-bold">Manage Workflows</h2>
                    <Link href={route('workflows.create')}>
                        <Button>Add Workflow</Button>
                    </Link>
                </div>
                <p className="mt-2 text-muted-foreground">
                    A list of your N8N workflows will appear here.
                </p>

                <div className="flex flex-wrap gap-4 mt-4">
                    <Input
                        placeholder="Search workflows..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        className="max-w-sm"
                    />
                    <Select value={status} onValueChange={(value) => setStatus(value)}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Filter by Status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="">All Statuses</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                    </Select>
                    <Input
                        type="number"
                        placeholder="Min Executions"
                        value={minExecutions}
                        onChange={(e) => setMinExecutions(e.target.value)}
                        className="w-[180px]"
                    />
                    <Input
                        type="number"
                        placeholder="Max Executions"
                        value={maxExecutions}
                        onChange={(e) => setMaxExecutions(e.target.value)}
                        className="w-[180px]"
                    />
                    <Input
                        type="date"
                        placeholder="Last Executed Before"
                        value={lastExecutedBefore}
                        onChange={(e) => setLastExecutedBefore(e.target.value)}
                        className="w-[180px]"
                    />
                    <Input
                        type="date"
                        placeholder="Last Executed After"
                        value={lastExecutedAfter}
                        onChange={(e) => setLastExecutedAfter(e.target.value)}
                        className="w-[180px]"
                    />
                </div>

                <div className="mt-8">
                    {workflows.length === 0 ? (
                        <p className="text-muted-foreground">
                            No workflows found matching your criteria.
                        </p>
                    ) : (
                        <ul className="space-y-4">
                            {workflows.map((workflow) => (
                                <li key={workflow.id} className="p-4 border rounded-lg flex items-center justify-between">
                                    <Link href={route('workflows.show', workflow.id)}>
                                        <div>
                                            <p className="font-semibold">{workflow.name}</p>
                                            <p className="text-sm text-muted-foreground">{workflow.n8n_workflow_id}</p>
                                        </div>
                                    </Link>
                                    <div className="flex items-center space-x-2">
                                        <Link href={route('analytics.workflow', workflow.id)}>
                                            <Button variant="outline" size="sm">Analytics</Button>
                                        </Link>
                                        <Switch
                                            checked={workflow.is_active}
                                            onCheckedChange={() => handleToggle(workflow.id, !workflow.is_active)}
                                        />
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}