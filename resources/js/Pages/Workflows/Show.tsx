import React from 'react';
import AppLayout from '@/Layouts/AppLayout';
import { PageProps } from '@/types';
import React<PERSON>low, { Controls, Background } from 'reactflow';

import 'reactflow/dist/style.css';

interface Workflow {
    id: number;
    n8n_instance_id: number;
    n8n_workflow_id: string;
    name: string;
    is_active: boolean;
    tags: string[];
    nodes: any[];
    connections: any[];
    last_sync_at: string;
}

export default function Show({ workflow }: PageProps<{ workflow: Workflow }>) {
    // Transform n8n nodes/connections to React Flow format
    const initialNodes = workflow.nodes.map((node: any) => ({
        id: node.id,
        type: node.type,
        position: { x: node.position.x, y: node.position.y },
        data: { label: node.name },
    }));

    const initialEdges = workflow.connections.map((connection: any) => ({
        id: `e${connection.fromNode}-${connection.toNode}`,
        source: connection.fromNode,
        target: connection.toNode,
        sourceHandle: connection.fromHandle,
        targetHandle: connection.toHandle,
    }));

    return (
        <AppLayout>
            <div className="p-8">
                <h2 className="text-3xl font-bold">Workflow: {workflow.name}</h2>
                <p className="mt-2 text-muted-foreground">ID: {workflow.n8n_workflow_id}</p>
                <p className="text-sm text-muted-foreground">Status: {workflow.is_active ? 'Active' : 'Inactive'}</p>

                <div style={{ width: '100%', height: '500px' }} className="mt-8 border rounded-lg">
                    <ReactFlow
                        nodes={initialNodes}
                        edges={initialEdges}
                        fitView
                    >
                        <Controls />
                        <Background gap={12} size={1} />
                    </ReactFlow>
                </div>
            </div>
        </AppLayout>
    );
}
