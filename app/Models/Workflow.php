<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Workflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'n8n_instance_id',
        'n8n_workflow_id',
        'name',
        'is_active',
        'tags',
        'nodes',
        'connections',
        'last_sync_at',
    ];

    protected $casts = [
        'tags' => 'array',
        'nodes' => 'array',
        'connections' => 'array',
        'is_active' => 'boolean',
        'last_sync_at' => 'datetime',
    ];

    public function n8nInstance(): BelongsTo
    {
        return $this->belongsTo(N8nInstance::class);
    }

    public function workflowExecutions(): HasMany
    {
        return $this->hasMany(WorkflowExecution::class);
    }
}
