<?php

namespace App\Models;

use App\Events\WorkflowExecutionUpdated;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowExecution extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        static::created(function (WorkflowExecution $workflowExecution) {
            WorkflowExecutionUpdated::dispatch($workflowExecution);
        });

        static::updated(function (WorkflowExecution $workflowExecution) {
            WorkflowExecutionUpdated::dispatch($workflowExecution);
        });
    }

    protected $fillable = [
        'workflow_id',
        'n8n_execution_id',
        'status',
        'started_at',
        'finished_at',
        'execution_time',
        'data',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'data' => 'array',
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }
}
