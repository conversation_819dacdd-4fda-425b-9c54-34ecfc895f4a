<?php

namespace App\Http\Controllers;

use App\Models\N8nInstance;
use Illuminate\Http\Request;
use Inertia\Inertia;

class N8nInstanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Inertia::render('Instances/Index', [
            'instances' => N8nInstance::all(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Instances/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|unique:n8n_instances,url',
            'api_key' => 'required|string',
        ]);

        N8nInstance::create([
            'name' => $validated['name'],
            'url' => $validated['url'],
            'api_key' => encrypt($validated['api_key']),
        ]);

        return redirect()->route('instances.index');
    }

    public function checkHealth(N8nInstance $n8nInstance)
    {
        $n8nApiService = new \App\Services\N8nApiService($n8nInstance->url, decrypt($n8nInstance->api_key));
        $n8nInstance->health_status = $n8nApiService->checkHealth() ? 'online' : 'offline';
        $n8nInstance->last_checked_at = now();
        $n8nInstance->save();

        return response()->json($n8nInstance);
    }
}
