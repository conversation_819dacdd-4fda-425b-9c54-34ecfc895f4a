<?php

namespace App\Http\Controllers;

use App\Models\Workflow;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WorkflowController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Synchronize workflows from N8N instances
        $n8nInstances = \App\Models\N8nInstance::all();

        foreach ($n8nInstances as $instance) {
            $n8nApiService = new \App\Services\N8nApiService($instance->url, decrypt($instance->api_key));
            $n8nWorkflows = $n8nApiService->getWorkflows();

            foreach ($n8nWorkflows as $n8nWorkflow) {
                Workflow::updateOrCreate(
                    [
                        'n8n_instance_id' => $instance->id,
                        'n8n_workflow_id' => $n8nWorkflow['id'],
                    ],
                    [
                        'name' => $n8nWorkflow['name'],
                        'is_active' => $n8nWorkflow['active'],
                        'nodes' => $n8nWorkflow['nodes'],
                        'connections' => $n8nWorkflow['connections'],
                        'last_sync_at' => now(),
                    ]
                );
            }
        }

        // Apply search, filter, and tag
        $workflows = Workflow::with('n8nInstance');

        if ($request->has('search')) {
            $workflows->where('name', 'like', '%' . $request->input('search') . '%')
                      ->orWhere('n8n_workflow_id', 'like', '%' . $request->input('search') . '%');
        }

        if ($request->has('status') && in_array($request->input('status'), ['active', 'inactive'])) {
            $workflows->where('is_active', $request->input('status') === 'active');
        }

        if ($request->has('tag')) {
            $workflows->whereJsonContains('tags', $request->input('tag'));
        }

        // Apply execution history filters
        if ($request->has('min_executions') || $request->has('max_executions') || $request->has('last_executed_before') || $request->has('last_executed_after')) {
            $workflows->withCount('workflowExecutions');

            if ($request->has('min_executions')) {
                $workflows->has('workflowExecutions', '>=', $request->input('min_executions'));
            }

            if ($request->has('max_executions')) {
                $workflows->has('workflowExecutions', '<=', $request->input('max_executions'));
            }

            if ($request->has('last_executed_before')) {
                $workflows->whereHas('workflowExecutions', function ($query) use ($request) {
                    $query->where('started_at', '<=', $request->input('last_executed_before'));
                });
            }

            if ($request->has('last_executed_after')) {
                $workflows->whereHas('workflowExecutions', function ($query) use ($request) {
                    $query->where('started_at', '>=', $request->input('last_executed_after'));
                });
            }
        }

        return Inertia::render('Workflows/Index', [
            'workflows' => $workflows->get(),
            'filters' => [
                'search' => $request->input('search', ''),
                'status' => $request->input('status', ''),
                'tag' => $request->input('tag', ''),
                'min_executions' => $request->input('min_executions', ''),
                'max_executions' => $request->input('max_executions', ''),
                'last_executed_before' => $request->input('last_executed_before', ''),
                'last_executed_after' => $request->input('last_executed_after', ''),
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Workflows/Create', [
            'instances' => \App\Models\N8nInstance::all(['id', 'name']),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'n8n_instance_id' => 'required|exists:n8n_instances,id',
            'n8n_workflow_id' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'is_active' => 'boolean',
            'tags' => 'nullable|string', // Will be cast to array in model
            'nodes' => 'required|string', // Expect JSON string from frontend
            'connections' => 'required|string', // Expect JSON string from frontend
        ]);

        // Convert tags string to array
        if (isset($validated['tags'])) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        }

        Workflow::create($validated);

        return redirect()->route('workflows.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Workflow $workflow)
    {
        $workflow->load('n8nInstance'); // Eager load the n8nInstance relationship
        return Inertia::render('Workflows/Show', [
            'workflow' => $workflow,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Workflow $workflow)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Workflow $workflow)
    {
        $validated = $request->validate([
            'is_active' => 'boolean',
        ]);

        $n8nApiService = new \App\Services\N8nApiService(
            $workflow->n8nInstance->url,
            decrypt($workflow->n8nInstance->api_key)
        );

        if ($validated['is_active']) {
            $n8nApiService->activateWorkflow($workflow->n8n_workflow_id);
        } else {
            $n8nApiService->deactivateWorkflow($workflow->n8n_workflow_id);
        }

        $workflow->update($validated);

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Workflow $workflow)
    {
        //
    }

    public function retryExecution(Workflow $workflow, WorkflowExecution $workflowExecution)
    {
        // In a real scenario, you would trigger a retry on the n8n instance
        // For now, we'll just update the status to 'retrying' or similar
        $workflowExecution->update(['status' => 'retrying']);

        return redirect()->back()->with('success', 'Execution marked for retry.');
    }

    public function cancelExecution(Workflow $workflow, WorkflowExecution $workflowExecution)
    {
        // In a real scenario, you would send a cancel command to the n8n instance
        // For now, we'll just update the status to 'cancelled'
        $workflowExecution->update(['status' => 'cancelled']);

        return redirect()->back()->with('success', 'Execution cancelled.');
    }
}
