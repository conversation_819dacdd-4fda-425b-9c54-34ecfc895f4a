<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\N8nInstance;
use App\Models\Workflow;
use App\Models\WorkflowExecution;


class DashboardController extends Controller
{
    public function index()
    {
        // Fetch data for metric cards
        $totalExecutions = WorkflowExecution::count();
        $successfulExecutions = WorkflowExecution::where('status', 'success')->count();
        $failedExecutions = WorkflowExecution::where('status', 'failed')->count();
        $avgExecutionTime = WorkflowExecution::avg('execution_time');

        // Fetch data for monthly executions chart
        $monthlyExecutions = WorkflowExecution::selectRaw('MONTHNAME(started_at) as month, COUNT(*) as executions')
            ->groupBy('month')
            ->orderByRaw('MIN(started_at)')
            ->get()
            ->toArray();

        // Fetch data for recent activity
        $recentExecutions = WorkflowExecution::with('workflow')->latest()->take(5)->get();

        // Fetch data for top workflows
        $topWorkflows = Workflow::withCount('executions')->orderByDesc('executions_count')->take(5)->get();

        return response()->json([
            'totalExecutions' => $totalExecutions,
            'successfulExecutions' => $successfulExecutions,
            'failedExecutions' => $failedExecutions,
            'avgExecutionTime' => round($avgExecutionTime / 1000, 2), // Convert ms to seconds
            'monthlyExecutions' => $monthlyExecutions,
            'recentExecutions' => $recentExecutions,
            'topWorkflows' => $topWorkflows,
        ]);
    }
}
