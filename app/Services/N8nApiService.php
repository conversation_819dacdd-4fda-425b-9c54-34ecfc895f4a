<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class N8nApiService
{
    protected Client $client;
    protected string $baseUrl;
    protected string $apiKey;

    public function __construct(string $baseUrl, string $apiKey)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
        $this->client = new Client([
            'base_uri' => $this->baseUrl . '/api/v1/',
            'headers' => [
                'X-N8N-API-KEY' => $this->apiKey,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'verify' => false, // For development, set to true in production with proper CA certs
        ]);
    }

    public function checkHealth(): bool
    {
        try {
            $response = $this->client->get('healthz');
            return $response->getStatusCode() === 200;
        } catch (GuzzleException $e) {
            // Log the error, but return false for health check
            \Log::error("N8N Health Check failed for {$this->baseUrl}: " . $e->getMessage());
            return false;
        }
    }

    public function getWorkflows(): array
    {
        try {
            $response = $this->client->get('workflows');
            return json_decode($response->getBody()->getContents(), true)['data'];
        } catch (GuzzleException $e) {
            \Log::error("Failed to fetch workflows from {$this->baseUrl}: " . $e->getMessage());
            return [];
        }
    }

    public function activateWorkflow(string $workflowId): bool
    {
        try {
            $response = $this->client->post("workflows/{$workflowId}/activate");
            return $response->getStatusCode() === 200;
        } catch (GuzzleException $e) {
            \Log::error("Failed to activate workflow {$workflowId} on {$this->baseUrl}: " . $e->getMessage());
            return false;
        }
    }

    public function deactivateWorkflow(string $workflowId): bool
    {
        try {
            $response = $this->client->post("workflows/{$workflowId}/deactivate");
            return $response->getStatusCode() === 200;
        } catch (GuzzleException $e) {
            \Log::error("Failed to deactivate workflow {$workflowId} on {$this->baseUrl}: " . $e->getMessage());
            return false;
        }
    }
}
