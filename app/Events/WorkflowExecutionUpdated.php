<?php

namespace App\Events;

use App\Models\WorkflowExecution;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkflowExecutionUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public WorkflowExecution $workflowExecution;

    /**
     * Create a new event instance.
     */
    public function __construct(WorkflowExecution $workflowExecution)
    {
        $this->workflowExecution = $workflowExecution;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('workflow-executions'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workflow.execution.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->workflowExecution->id,
            'workflow_id' => $this->workflowExecution->workflow_id,
            'n8n_execution_id' => $this->workflowExecution->n8n_execution_id,
            'status' => $this->workflowExecution->status,
            'started_at' => $this->workflowExecution->started_at,
            'finished_at' => $this->workflowExecution->finished_at,
            'execution_time' => $this->workflowExecution->execution_time,
            'data' => $this->workflowExecution->data,
        ];
    }
}
