<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workflows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('n8n_instance_id')->constrained()->onDelete('cascade');
            $table->string('n8n_workflow_id');
            $table->string('name');
            $table->boolean('is_active')->default(false);
            $table->json('tags')->nullable();
            $table->json('nodes');
            $table->json('connections');
            $table->timestamp('last_sync_at')->nullable();
            $table->timestamps();

            $table->unique(['n8n_instance_id', 'n8n_workflow_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflows');
    }
};
