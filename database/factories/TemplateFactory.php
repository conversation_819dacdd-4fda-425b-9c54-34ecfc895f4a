<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Template>
 */
class TemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'tags' => json_encode($this->faker->words(5)),
            'schema' => json_encode([
                'nodes' => [
                    ['name' => 'Start'],
                    ['name' => 'End'],
                ],
            ]),
        ];
    }
}
