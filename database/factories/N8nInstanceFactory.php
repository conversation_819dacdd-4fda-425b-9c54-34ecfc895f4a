<?php

namespace Database\Factories;

use App\Models\N8nInstance;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Crypt;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\N8nInstance>
 */
class N8nInstanceFactory extends Factory
{
    protected $model = N8nInstance::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'url' => $this->faker->url,
            'api_key' => encrypt($this->faker->uuid), // Encrypt a fake API key
            'health_status' => $this->faker->randomElement(['online', 'offline', 'degraded']),
            'last_checked_at' => $this->faker->dateTimeThisYear(),
        ];
    }
}
