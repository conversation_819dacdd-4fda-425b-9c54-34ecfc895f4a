<?php

namespace Database\Factories;

use App\Models\WorkflowExecution;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WorkflowExecution>
 */
class WorkflowExecutionFactory extends Factory
{
    protected $model = WorkflowExecution::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'workflow_id' => \App\Models\Workflow::factory(),
            'n8n_execution_id' => $this->faker->uuid,
            'status' => $this->faker->randomElement(['success', 'failed']),
            'started_at' => $this->faker->dateTimeThisYear(),
            'finished_at' => $this->faker->dateTimeThisYear(),
            'execution_time' => $this->faker->numberBetween(100, 5000),
            'data' => json_encode(['log' => $this->faker->sentence()]), // Placeholder for data
        ];
    }
}
