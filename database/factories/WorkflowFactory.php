<?php

namespace Database\Factories;

use App\Models\Workflow;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Workflow>
 */
class WorkflowFactory extends Factory
{
    protected $model = Workflow::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'n8n_instance_id' => \App\Models\N8nInstance::factory(),
            'n8n_workflow_id' => $this->faker->uuid,
            'name' => $this->faker->sentence(3),
            'is_active' => $this->faker->boolean,
            'tags' => $this->faker->words(3),
            'nodes' => json_encode([]), // Placeholder for nodes
            'connections' => json_encode([]), // Placeholder for connections
            'last_sync_at' => $this->faker->dateTimeThisYear(),
        ];
    }
}
