<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker; // Import Faker

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $faker = Faker::create(); // Instantiate Faker

        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);



        // Create demo N8n Instances
        \App\Models\N8nInstance::factory(5)->create()->each(function ($instance) use ($faker) { // Pass $faker to outer closure
            // Create workflows for each instance
            \App\Models\Workflow::factory(rand(5, 10))->create([
                'n8n_instance_id' => $instance->id,
            ])->each(function ($workflow) use ($faker) { // Pass $faker to inner closure
                // Create a guaranteed number of successful and failed workflow executions
                \App\Models\WorkflowExecution::factory(5)->create([
                    'workflow_id' => $workflow->id,
                    'status' => 'success',
                    'execution_time' => $faker->numberBetween(100, 1000),
                ]);

                \App\Models\WorkflowExecution::factory(2)->create([
                    'workflow_id' => $workflow->id,
                    'status' => 'failed',
                    'execution_time' => $faker->numberBetween(50, 500),
                ]);

                // Create some additional random executions
                \App\Models\WorkflowExecution::factory(rand(3, 8))->create([
                    'workflow_id' => $workflow->id,
                ]);
            });
        });
    }
}