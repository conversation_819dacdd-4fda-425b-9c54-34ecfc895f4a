# Docker Deployment Guide: N8N Laravel Dashboard

This guide provides advanced deployment instructions for the N8N Laravel Dashboard using Docker, focusing on production environments, SSL/HTTPS setup, and custom configurations.

## Table of Contents

1.  [Prerequisites](#1-prerequisites)
2.  [Production Environment Setup](#2-production-environment-setup)
    *   [Environment Variables](#environment-variables)
    *   [Database Persistence](#database-persistence)
    *   [Nginx Configuration](#nginx-configuration)
3.  [SSL/HTTPS Configuration](#3-sslhttps-configuration)
    *   [Using Certbot (Recommended)](#using-certbot-recommended)
    *   [Manual SSL Configuration](#manual-ssl-configuration)
4.  [Custom Ports](#4-custom-ports)
5.  [Scaling Services](#5-scaling-services)
6.  [Troubleshooting](#6-troubleshooting)

## 1. Prerequisites

-   A server with <PERSON><PERSON> and <PERSON>er Compose installed.
-   A registered domain name pointing to your server's IP address.
-   Basic understanding of Docker, Nginx, and Linux command line.

## 2. Production Environment Setup

### Environment Variables

Before deploying to production, ensure your `.env` file is properly configured with secure and production-ready values.

-   `APP_ENV=production`
-   `APP_DEBUG=false`
-   `APP_KEY`: Generate a strong, unique key using `docker-compose exec app php artisan key:generate`.
-   `APP_URL`: Set this to your production domain (e.g., `https://yourdomain.com`).
-   `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD`: Use strong, unique credentials for your database.
-   `REDIS_PASSWORD`: Set a strong password for Redis if exposed externally.
-   `REVERB_APP_KEY`, `REVERB_APP_SECRET`: Use strong, unique keys for Reverb.

### Database Persistence

Ensure your database data persists across container restarts. The `docker-compose.yml` uses a named volume (`./docker/dbdata`) for MySQL data. For production, consider using a dedicated managed database service or a more robust Docker volume strategy.

### Nginx Configuration

The default `docker/nginx/app.conf` is configured for HTTP. For production, you will primarily use HTTPS. The `docker/nginx/nginx.conf` includes `conf.d/*.conf`, allowing you to add more specific server blocks for SSL.

## 3. SSL/HTTPS Configuration

It is highly recommended to use SSL/HTTPS for production deployments. This guide focuses on using Certbot with Nginx.

### Using Certbot (Recommended)

Certbot automates the process of obtaining and renewing Let's Encrypt SSL certificates.

1.  **Modify `docker-compose.yml` for Certbot:**

    Add a `certbot` service and modify the `nginx` service to expose port 80 and 443, and include a volume for Certbot certificates.

    ```yaml
    # ... other services

    nginx:
      # ... existing config
      ports:
        - "80:80"
        - "443:443"
      volumes:
        # ... existing volumes
        - ./data/certbot/conf:/etc/letsencrypt
        - ./data/certbot/www:/var/www/certbot

    certbot:
      image: certbot/certbot
      container_name: certbot
      volumes:
        - ./data/certbot/conf:/etc/letsencrypt
        - ./data/certbot/www:/var/www/certbot
      command: certonly --webroot -w /var/www/certbot --email <EMAIL> -d yourdomain.com --agree-tos --no-eff-email --force-renewal
      # Replace <EMAIL> and yourdomain.com
    ```

2.  **Prepare Nginx for Certbot:**

    Create a temporary Nginx configuration (`docker/nginx/conf.d/default.conf`) to handle Certbot's challenge. This should be a simple server block listening on port 80.

    ```nginx
    server {
        listen 80;
        server_name yourdomain.com;

        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }

        location / {
            return 301 https://$host$request_uri;
        }
    }
    ```

3.  **Run Certbot:**

    Start Nginx and then run Certbot to obtain the certificates:

    ```bash
    docker-compose up --force-recreate --no-deps -d nginx
    docker-compose run --rm certbot
    ```

4.  **Configure Nginx for SSL:**

    Modify `docker/nginx/app.conf` (or create a new SSL-specific config) to use the obtained SSL certificates.

    ```nginx
    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

        # ... other SSL settings (ciphers, protocols, etc.)

        root /var/www/html/public;
        index index.php index.html;

        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass app:9000;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }

        location / {
            try_files $uri $uri/ /index.php?$query_string;
            gzip_static on;
        }
    }
    ```

5.  **Restart Nginx:**

    ```bash
    docker-compose restart nginx
    ```

6.  **Automate Renewal:**

    Add a cron job or a Docker Compose healthcheck/restart policy to automatically renew certificates.

### Manual SSL Configuration

If you have existing SSL certificates, you can manually place them in a mounted volume (e.g., `./data/nginx/ssl`) and configure Nginx to use them.

## 4. Custom Ports

To change the exposed ports, modify the `ports` section in `docker-compose.yml` for the `nginx`, `db`, and `redis` services.

```yaml
nginx:
  ports:
    - "80:80" # Host:Container

db:
  ports:
    - "3306:3306"

redis:
  ports:
    - "6379:6379"
```

## 5. Scaling Services

For higher availability and performance, you can scale services using `docker-compose up --scale`.

```bash
# Scale the app service to 3 replicas
docker-compose up -d --scale app=3
```

## 6. Troubleshooting

-   **Container logs:** Check container logs for errors:
    ```bash
    docker-compose logs <service_name>
    ```
-   **Firewall:** Ensure necessary ports (80, 443, etc.) are open on your server's firewall.
-   **Permissions:** Verify correct file permissions for mounted volumes.

For general application troubleshooting, refer to [INSTALLATION.md](INSTALLATION.md).
