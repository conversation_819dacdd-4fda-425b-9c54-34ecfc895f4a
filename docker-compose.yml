
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: n8n-dashboard-app
    restart: unless-stopped
    volumes:
      - ./:/var/www/html
      - ./docker/php/www.conf:/usr/local/etc/php-fpm.d/www.conf
    networks:
      - n8n-dashboard-network

  nginx:
    image: nginx:alpine
    container_name: n8n-dashboard-nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - n8n-dashboard-network

  db:
    image: mysql:8.0
    container_name: n8n-dashboard-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-n8n_dashboard}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_USER: ${DB_USERNAME:-user}
    volumes:
      - ./docker/dbdata:/var/lib/mysql
    ports:
      - "33061:3306"
    networks:
      - n8n-dashboard-network

  redis:
    image: redis:latest
    container_name: n8n-dashboard-redis
    restart: unless-stopped
    ports:
      - "63791:6379"
    networks:
      - n8n-dashboard-network

  reverb:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: n8n-dashboard-reverb
    restart: unless-stopped
    command: php artisan reverb:start --host=0.0.0.0 --port=9000
    ports:
      - "9000:9000"
    volumes:
      - ./:/var/www/html
    depends_on:
      - app
      - redis
    networks:
      - n8n-dashboard-network

networks:
  n8n-dashboard-network:
    driver: bridge
