#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

PROJECT_NAME="n8n-laravel-dashboard"
OUTPUT_DIR="./dist"
OUTPUT_ZIP="${OUTPUT_DIR}/${PROJECT_NAME}.zip"

# Create output directory if it doesn't exist
mkdir -p "${OUTPUT_DIR}"

# Clean up previous build artifacts
rm -f "${OUTPUT_ZIP}"

# List of files/directories to exclude from the zip
EXCLUDE_LIST=(
    ".git"
    ".idea"
    ".vscode"
    "node_modules"
    "vendor"
    "storage/app"
    "storage/framework"
    "storage/logs"
    ".env"
    ".env.example"
    "setup.sh"
    "package.sh"
    "dist"
    "docker/dbdata"
)

# Build the exclude options for tar
EXCLUDE_OPTIONS=""
for item in "${EXCLUDE_LIST[@]}"; do
    EXCLUDE_OPTIONS+=" --exclude=${item}"
done

echo "Creating zip archive: ${OUTPUT_ZIP}"

# Create the zip archive using tar and gzip, then convert to zip
# Using tar to handle exclusions more robustly, then piping to zip
# This creates a temporary tar.gz and then zips it, which is a bit convoluted
# A simpler way is to use `zip -r` directly with -x options

# Let's use zip -r directly for simplicity and better cross-platform compatibility
zip -r "${OUTPUT_ZIP}" . -x@ <(printf '%s\n' "${EXCLUDE_LIST[@]}")

# Alternative using tar (more robust for complex exclusions, but requires conversion)
# tar -czf - . ${EXCLUDE_OPTIONS} | gzip > "${OUTPUT_ZIP}.tar.gz"
# zip -j "${OUTPUT_ZIP}" "${OUTPUT_ZIP}.tar.gz"
# rm "${OUTPUT_ZIP}.tar.gz"

echo "Archive created successfully!"
ls -lh "${OUTPUT_ZIP}"
