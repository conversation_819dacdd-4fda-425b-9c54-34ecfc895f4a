#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

echo "Starting N8N Dashboard setup..."

# 1. Copy .env.example to .env if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cp .env.example .env
else
    echo ".env file already exists. Skipping creation."
fi

# 2. Update .env for Docker environment
echo "Updating .env for Docker configuration..."
# Database settings
sed -i 's/^DB_CONNECTION=sqlite/DB_CONNECTION=mysql/' .env
sed -i 's/^# DB_HOST=127.0.0.1/DB_HOST=db/' .env
sed -i 's/^# DB_PORT=3306/DB_PORT=3306/' .env
sed -i 's/^# DB_DATABASE=laravel/DB_DATABASE=n8n_dashboard/' .env
sed -i 's/^# DB_USERNAME=root/DB_USERNAME=user/' .env
sed -i 's/^# DB_PASSWORD=/DB_PASSWORD=secret/' .env

# Redis settings
sed -i 's/^REDIS_HOST=127.0.0.1/REDIS_HOST=redis/' .env
sed -i 's/^QUEUE_CONNECTION=database/QUEUE_CONNECTION=redis/' .env
sed -i 's/^CACHE_STORE=database/CACHE_STORE=redis/' .env
sed -i 's/^SESSION_DRIVER=database/SESSION_DRIVER=redis/' .env

# Reverb settings
sed -i 's/^BROADCAST_CONNECTION=log/BROADCAST_CONNECTION=reverb/' .env
if ! grep -q "REVERB_APP_ID" .env; then
    echo '
# Reverb Configuration
REVERB_APP_ID=n8n_dashboard
REVERB_APP_KEY=reverb_key
REVERB_APP_SECRET=reverb_secret
REVERB_HOST="reverb"
REVERB_PORT=9000
REVERB_SCHEME=http
' >> .env
fi

echo "Building and starting Docker containers..."
docker-compose up -d --build

echo "Installing Composer dependencies..."
docker-compose exec app composer install

echo "Generating application key..."
docker-compose exec app php artisan key:generate

echo "Running database migrations..."
docker-compose exec app php artisan migrate

echo "N8N Dashboard setup complete!"
echo "You can now access the application at http://localhost:8000"
