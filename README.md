# N8N Laravel Dashboard

A professional, full-stack workflow automation dashboard for managing multiple N8N instances, combining the strengths of Laravel 12 (PHP) and React 19 (TypeScript) with a modern UI toolkit (shadcn/ui and Tailwind CSS) and Inertia.

## Features

-   **Multi-instance Management:** Support for connecting, monitoring, and configuring multiple N8N servers.
-   **Advanced Analytics Dashboard:** Real-time charts and trends for workflow success/failure rates, execution times, and total executions.
-   **Workflow Administration:** List, search, filter, tag, activate/deactivate, and bulk-operate workflows. Visualize workflow graphs, display execution logs, and manage error histories.
-   **Template Marketplace:** Offer workflows as templates, searchable by integration, trigger, and business logic. Enable import/export of full workflow schemas.
-   **Mobile-responsive UI:** Optimized theme provider and toggle (dark/light/system), sidebar navigation, and touch-friendly data tables.
-   **Automated Deployment (Docker):** Dockerfile and Docker Compose for app, nginx, MySQL/Postgres, Redis, Reverb WebSocket. Includes quick setup scripts for environment provisioning and one-command installation.

## Installation

For detailed installation instructions, please refer to [INSTALLATION.md](INSTALLATION.md).

## Docker Deployment

For detailed Docker deployment instructions, please refer to [DOCKER_DEPLOYMENT_GUIDE.md](DOCKER_DEPLOYMENT_GUIDE.md).

## Contributing

Contributions are welcome! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for details.

## License

This project is open-sourced under the [MIT License](LICENSE.md).