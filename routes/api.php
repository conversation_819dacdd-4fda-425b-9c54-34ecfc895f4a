<?php

use App\Http\Controllers\DashboardController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/dashboard-data', [DashboardController::class, 'index']);

Route::post('/n8n/webhook', [N8nWebhookController::class, 'handleWebhook'])->middleware('n8n.webhook.verify');
