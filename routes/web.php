<?php

use App\Http\Controllers\N8nInstanceController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\WorkflowController;
use Illuminate\Support\Facades\Route;
use App\Models\Workflow;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Dashboard/Index');
})->name('dashboard');

Route::resource('instances', N8nInstanceController::class);
Route::post('/instances/{n8n_instance}/check-health', [N8nInstanceController::class, 'checkHealth'])->name('instances.check-health');

Route::get('/instances/{n8n_instance}/analytics', function (App\Models\N8nInstance $n8n_instance) {
    return Inertia::render('Instances/Analytics', [
        'instance' => $n8n_instance,
    ]);
})->name('instances.analytics');
Route::resource('workflows', WorkflowController::class);

Route::get('/workflows/{workflow}/executions', function (App\Models\Workflow $workflow) {
    return Inertia::render('Workflows/Executions', [
        'workflow' => $workflow,
        'executions' => $workflow->workflowExecutions()->latest()->get(),
    ]);
})->name('workflows.executions.index');

Route::resource('templates', TemplateController::class);

Route::get('/workflows/{workflow}/analytics', function (App\Models\Workflow $workflow) {
    return Inertia::render('Workflows/Analytics', [
        'workflow' => $workflow,
    ]);
})->name('analytics.workflow');

Route::post('/workflows/{workflow}/executions/{workflowExecution}/retry', [WorkflowController::class, 'retryExecution'])->name('workflows.executions.retry');
Route::post('/workflows/{workflow}/executions/{workflowExecution}/cancel', [WorkflowController::class, 'cancelExecution'])->name('workflows.executions.cancel');
