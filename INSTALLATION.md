# Installation Guide: N8N Laravel Dashboard

This guide will walk you through the steps to set up and run the N8N Laravel Dashboard on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

-   **Docker Desktop:** Required for containerization (includes Docker Engine and Docker Compose).
    -   [Download Docker Desktop](https://www.docker.com/products/docker-desktop)
-   **Git:** For cloning the repository.
    -   [Download Git](https://git-scm.com/downloads)

## Step-by-Step Installation

1.  **Clone the Repository:**

    Open your terminal or command prompt and clone the project repository:

    ```bash
    git clone https://github.com/your-username/n8n-laravel-dashboard.git
    cd n8n-laravel-dashboard
    ```

2.  **Run the Setup Script:**

    The project includes a convenient setup script that automates the environment provisioning, Docker setup, and initial application configuration. Make sure the script is executable and then run it:

    ```bash
    chmod +x setup.sh
    ./setup.sh
    ```

    This script will perform the following actions:

    -   Create a `.env` file from `.env.example`.
    -   Configure the `.env` file for Docker (database, Redis, Reverb settings).
    -   Build and start the Docker containers (app, nginx, db, redis, reverb).
    -   Install PHP dependencies using Composer inside the `app` container.
    -   Generate the Laravel application key.
    -   Run database migrations to set up the necessary tables.
    -   Seed the database with demo data for out-of-box testing.

    **Note:** The first time you run `./setup.sh`, it might take a few minutes as Docker images need to be downloaded and built.

3.  **Access the Application:**

    Once the `setup.sh` script completes successfully, the application will be accessible in your web browser at:

    ```
    http://localhost:8000
    ```

    You can log in with the following demo credentials:

    -   **Email:** `<EMAIL>`
    -   **Password:** `password`

## Common Issues & Troubleshooting

-   **`docker-compose up` fails:** Ensure Docker Desktop is running and that no other services are using ports `8000`, `33061`, `63791`, or `9000` on your host machine.
-   **Permissions errors:** If you encounter permission issues, especially on Linux, you might need to adjust the permissions of the project directory. For example:
    ```bash
    sudo chown -R $USER:$USER .
    sudo chmod -R 775 storage bootstrap/cache
    ```
-   **Node.js version warnings:** You might see warnings about Node.js versions during `npm install` or `npm run build`. While not critical for the application to run within Docker, it's recommended to use Node.js v20.19+ or v22.12+ for development on your host machine.

For more advanced deployment configurations, refer to [DOCKER_DEPLOYMENT_GUIDE.md](DOCKER_DEPLOYMENT_GUIDE.md).
