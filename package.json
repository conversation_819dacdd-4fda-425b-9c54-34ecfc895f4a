{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "tsc && vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.19.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.11.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "typescript": "^5.0.2", "vite": "^7.0.4"}, "dependencies": {"@inertiajs/inertia": "^0.11.1", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "laravel-echo": "^2.2.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.544.0", "pusher-js": "^8.4.0", "react-is": "^19.1.1", "reactflow": "^11.11.4", "recharts": "^3.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}}